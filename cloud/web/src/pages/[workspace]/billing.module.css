.root {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  padding: var(--space-7) var(--space-5) var(--space-5);

  [data-slot="billing-info"] {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
  }

  [data-slot="header"] {
    display: flex;
    flex-direction: column;
    gap: var(--space-1-5);

    h2 {
      text-transform: uppercase;
      font-weight: 600;
      letter-spacing: -0.03125rem;
      font-size: var(--font-size-lg);
    }

    p {
      color: var(--color-text-dimmed);
      font-size: var(--font-size-md);
    }
  }

  [data-slot="balance"] {
    display: flex;
    flex-direction: column;
    gap: var(--space-5);
    padding: var(--space-6);
    border: 2px solid var(--color-border);
  }

  [data-slot="amount"] {
    font-size: var(--font-size-3xl);
    font-weight: 600;
    line-height: 1.2;
  }

  @media (min-width: 40rem) {
    [data-slot="balance"] {
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
    }

    [data-slot="amount"] {
      margin: 0;
    }
  }
}
