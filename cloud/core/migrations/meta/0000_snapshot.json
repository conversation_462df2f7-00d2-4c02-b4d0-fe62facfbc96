{"id": "9b5cec8c-8b59-4d7a-bb5c-76ade1c83d6f", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.billing": {"name": "billing", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "time_created": {"name": "time_created", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "time_deleted": {"name": "time_deleted", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "payment_method_id": {"name": "payment_method_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "payment_method_last4": {"name": "payment_method_last4", "type": "<PERSON><PERSON><PERSON>(4)", "primaryKey": false, "notNull": false}, "balance": {"name": "balance", "type": "bigint", "primaryKey": false, "notNull": true}, "reload": {"name": "reload", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"billing_workspace_id_workspace_id_fk": {"name": "billing_workspace_id_workspace_id_fk", "tableFrom": "billing", "tableTo": "workspace", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"billing_workspace_id_id_pk": {"name": "billing_workspace_id_id_pk", "columns": ["workspace_id", "id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment": {"name": "payment", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "time_created": {"name": "time_created", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "time_deleted": {"name": "time_deleted", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "payment_id": {"name": "payment_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"payment_workspace_id_workspace_id_fk": {"name": "payment_workspace_id_workspace_id_fk", "tableFrom": "payment", "tableTo": "workspace", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"payment_workspace_id_id_pk": {"name": "payment_workspace_id_id_pk", "columns": ["workspace_id", "id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.usage": {"name": "usage", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "time_created": {"name": "time_created", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "time_deleted": {"name": "time_deleted", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "request_id": {"name": "request_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "input_tokens": {"name": "input_tokens", "type": "integer", "primaryKey": false, "notNull": true}, "output_tokens": {"name": "output_tokens", "type": "integer", "primaryKey": false, "notNull": true}, "reasoning_tokens": {"name": "reasoning_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "cache_read_tokens": {"name": "cache_read_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "cache_write_tokens": {"name": "cache_write_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"usage_workspace_id_workspace_id_fk": {"name": "usage_workspace_id_workspace_id_fk", "tableFrom": "usage", "tableTo": "workspace", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"usage_workspace_id_id_pk": {"name": "usage_workspace_id_id_pk", "columns": ["workspace_id", "id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "time_created": {"name": "time_created", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "time_deleted": {"name": "time_deleted", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "time_seen": {"name": "time_seen", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"user_email": {"name": "user_email", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_workspace_id_workspace_id_fk": {"name": "user_workspace_id_workspace_id_fk", "tableFrom": "user", "tableTo": "workspace", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_workspace_id_id_pk": {"name": "user_workspace_id_id_pk", "columns": ["workspace_id", "id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workspace": {"name": "workspace", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "time_created": {"name": "time_created", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "time_deleted": {"name": "time_deleted", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"slug": {"name": "slug", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}