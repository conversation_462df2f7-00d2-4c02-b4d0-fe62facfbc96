{"$schema": "https://json.schemastore.org/package.json", "version": "0.4.19", "name": "opencode", "type": "module", "private": true, "scripts": {"typecheck": "tsc --noEmit", "dev": "bun run --conditions=development ./src/index.ts"}, "bin": {"opencode": "./bin/opencode"}, "exports": {"./*": "./src/*.ts"}, "devDependencies": {"@ai-sdk/amazon-bedrock": "2.2.10", "@octokit/webhooks-types": "7.6.1", "@standard-schema/spec": "1.0.0", "@tsconfig/bun": "1.0.7", "@types/bun": "latest", "@types/turndown": "5.0.5", "@types/yargs": "17.0.33", "typescript": "catalog:", "vscode-languageserver-types": "3.17.5", "zod-to-json-schema": "3.24.5"}, "dependencies": {"@actions/core": "1.11.1", "@actions/github": "6.0.1", "@clack/prompts": "1.0.0-alpha.1", "@hono/zod-validator": "catalog:", "@modelcontextprotocol/sdk": "1.15.1", "@octokit/graphql": "9.0.1", "@octokit/rest": "22.0.0", "@openauthjs/openauth": "0.4.3", "@opencode-ai/plugin": "workspace:*", "@opencode-ai/sdk": "workspace:*", "@standard-schema/spec": "1.0.0", "@zip.js/zip.js": "2.7.62", "ai": "catalog:", "decimal.js": "10.5.0", "diff": "8.0.2", "gray-matter": "4.0.3", "hono": "catalog:", "hono-openapi": "0.4.8", "isomorphic-git": "1.32.1", "jsonc-parser": "3.3.1", "minimatch": "10.0.3", "open": "10.1.2", "remeda": "catalog:", "tree-sitter": "0.22.4", "tree-sitter-bash": "0.23.3", "turndown": "7.2.0", "vscode-jsonrpc": "8.2.1", "xdg-basedir": "5.1.0", "yargs": "18.0.0", "zod": "catalog:", "zod-openapi": "4.1.0"}}