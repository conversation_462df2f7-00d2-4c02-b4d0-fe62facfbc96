---
title: Providers
description: Using any LLM provider in opencode.
---

opencode uses the [AI SDK](https://ai-sdk.dev/) and [Models.dev](https://models.dev) to support for **75+ LLM providers** and it supports running local models.

To add a provider you need to:

1. Add the API keys for the provider using `opencode auth login`.
2. Configure the provider in your opencode config.

---

### Credentials

When you add a provider's API keys with `opencode auth login`, they are stored
in `~/.local/share/opencode/auth.json`.

---

### Config

You can customize the providers through the `provider` section in your opencode
config.

---

#### Base URL

You can customize the base URL for any provider by setting the `baseURL` option. This is useful when using proxy services or custom endpoints.

```json title="opencode.json" {6}
{
  "$schema": "https://opencode.ai/config.json",
  "provider": {
    "anthropic": {
      "options": {
        "baseURL": "https://api.anthropic.com/v1"
      }
    }
  }
}
```

---

## Custom provider

To add any **OpenAI-compatible** provider that's not listed in `opencode auth login`:

:::tip
You can use any OpenAI-compatible provider with opencode. Most modern AI providers offer OpenAI-compatible APIs.
:::

1. Run `opencode auth login` and scroll down to **Other**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ...
   │  ● Other
   └
   ```

2. Enter a unique ID for the provider.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Enter provider id
   │  myprovider
   └
   ```

   :::note
   Choose a memorable ID, you'll use this in your config file.
   :::

3. Enter your API key for the provider.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ▲  This only stores a credential for myprovider - you will need configure it in opencode.json, check the docs for examples.
   │
   ◇  Enter your API key
   │  sk-...
   └
   ```

4. Create or update your `opencode.json` file in your project directory:

   ```json title="opencode.json" ""myprovider"" {5-15}
   {
     "$schema": "https://opencode.ai/config.json",
     "provider": {
       "myprovider": {
         "npm": "@ai-sdk/openai-compatible",
         "name": "My AI ProviderDisplay Name",
         "options": {
           "baseURL": "https://api.myprovider.com/v1"
         },
         "models": {
           "my-model-name": {
             "name": "My Model Display Name"
           }
         }
       }
     }
   }
   ```

   Here are the configuration options:

   - **npm**: AI SDK package to use, `@ai-sdk/openai-compatible` for OpenAI-compatible providers
   - **name**: Display name in UI.
   - **models**: Available models.
   - **options.baseURL**: API endpoint URL.
   - **options.apiKey**: Optionally set the API key, if not using auth.
   - **options.headers**: Optionally set custom headers.

   More on the advanced options in the example below.

5. Run the `/models` command and your custom provider and models will appear in the selection list.

---

##### Example

Here's an example setting the `apiKey` and `headers` options.

```json title="opencode.json" {9,11}
{
  "$schema": "https://opencode.ai/config.json",
  "provider": {
    "myprovider": {
      "npm": "@ai-sdk/openai-compatible",
      "name": "My AI ProviderDisplay Name",
      "options": {
        "baseURL": "https://api.myprovider.com/v1",
        "apiKey": "{env:ANTHROPIC_API_KEY}",
        "headers": {
          "Authorization": "Bearer custom-token"
        }
      },
      "models": {
        "my-model-name": {
          "name": "My Model Display Name"
        }
      }
    }
  }
}
```

We are setting the `apiKey` using the `env` variable syntax, [learn more](/docs/config#env-vars).

---

## Directory

Let's look at some of the providers in detail. If you'd like to add a provider to the
list, feel free to open a PR.

:::note
Don't see a provider here? Submit a PR.
:::

---

### Amazon Bedrock

To use Amazon Bedrock with opencode:

1. Head over to the **Model catalog** in the Amazon Bedrock console and request
   access to the models you want.

   :::tip
   You need to have access to the model you want in Amazon Bedrock.
   :::

1. You'll need either to set one of the following environment variables:

   - `AWS_ACCESS_KEY_ID`: You can get this by creating an IAM user and generating
     an access key for it.
   - `AWS_PROFILE`: First login through AWS IAM Identity Center (or AWS SSO) using
     `aws sso login`. Then get the name of the profile you want to use.
   - `AWS_BEARER_TOKEN_BEDROCK`: You can generate a long-term API key from the
     Amazon Bedrock console.

   Once you have one of the above, set it while running opencode.

   ```bash
   AWS_ACCESS_KEY_ID=XXX opencode
   ```

   Or add it to a `.env` file in the project root.

   ```bash title=".env"
   AWS_ACCESS_KEY_ID=XXX
   ```

   Or add it to your bash profile.

   ```bash title="~/.bash_profile"
   export AWS_ACCESS_KEY_ID=XXX
   ```

1. Run the `/models` command to select the model you want.

---

### Anthropic

We recommend signing up for [Claude Pro](https://www.anthropic.com/news/claude-pro) or [Max](https://www.anthropic.com/max), it's the most cost-effective way to use opencode.

Once you've singed up, run `opencode auth login` and select Anthropic.

```bash
$ opencode auth login

┌  Add credential
│
◆  Select provider
│  ● Anthropic (recommended)
│  ○ OpenAI
│  ○ Google
│  ...
└
```

Here you can select the **Claude Pro/Max** option and it'll open your browser
and ask you to authenticate.

```bash
$ opencode auth login
┌  Add credential
│
◇  Select provider
│  Anthropic
│
◆  Login method
│  ● Claude Pro/Max
│  ○ Create API Key
│  ○ Manually enter API Key
└
```

Now all the the Anthropic models should be available when you use the `/models` command.

##### Using API keys

You can also select **Create API Key** if you don't have a Pro/Max subscription. It'll also open your browser and ask you to login to Anthropic and give you a code you can paste in your terminal.

Or if you already have an API key, you can select **Manually enter API Key** and paste it in your terminal.

---

### Azure OpenAI

1. Head over to the [Azure portal](https://portal.azure.com/) and create an **Azure OpenAI** resource. You'll need:

   - **Resource name**: This becomes part of your API endpoint (`https://RESOURCE_NAME.openai.azure.com/`)
   - **API key**: Either `KEY 1` or `KEY 2` from your resource

2. Go to [Azure AI Foundry](https://ai.azure.com/) and deploy a model.

   :::note
   The deployment name must match the model name for opencode to work properly.
   :::

3. Run `opencode auth login` and select **Azure**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● Azure
   │  ...
   └
   ```

4. Enter your API key.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  Azure
   │
   ◇  Enter your API key
   │  _
   └
   ```

5. Set your resource name as an environment variable:

   ```bash
   AZURE_RESOURCE_NAME=XXX opencode
   ```

   Or add it to a `.env` file in the project root:

   ```bash title=".env"
   AZURE_RESOURCE_NAME=XXX
   ```

   Or add it to your bash profile:

   ```bash title="~/.bash_profile"
   export AZURE_RESOURCE_NAME=XXX
   ```

6. Run the `/models` command to select your deployed model.

---

### Cerebras

1. Head over to the [Cerebras console](https://inference.cerebras.ai/), create an account, and generate an API key.

2. Run `opencode auth login` and select **Cerebras**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● Cerebras
   │  ...
   └
   ```

3. Enter your Cerebras API key.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  Cerebras
   │
   ◇  Enter your API key
   │  _
   └
   ```

4. Run the `/models` command to select a model like _Qwen 3 Coder 480B_.

---

### DeepSeek

1. Head over to the [DeepSeek console](https://platform.deepseek.com/), create an account, and click **Create new API key**.

2. Run `opencode auth login` and select **DeepSeek**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● DeepSeek
   │  ...
   └
   ```

3. Enter your DeepSeek API key.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  DeepSeek
   │
   ◇  Enter your API key
   │  _
   └
   ```

4. Run the `/models` command to select a DeepSeek model like _DeepSeek Reasoner_.

---

### Fireworks AI

1. Head over to the [Fireworks AI console](https://app.fireworks.ai/), create an account, and click **Create API Key**.

2. Run `opencode auth login` and select **Fireworks AI**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● Fireworks AI
   │  ...
   └
   ```

3. Enter your Fireworks AI API key.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  Fireworks AI
   │
   ◇  Enter your API key
   │  _
   └
   ```

4. Run the `/models` command to select a model like _Kimi K2 Instruct_.

---

### GitHub Copilot

To use your GitHub Copilot subscription with opencode:

:::note
Some models might need a [Pro+
subscription](https://github.com/features/copilot/plans) to use.
:::

1. Run `opencode auth login` and select GitHub Copilot.

   ```bash
   $ opencode auth login
   ┌  Add credential

   │
   ◇  Select provider
   │  GitHub Copilot
   │
   ◇   ──────────────────────────────────────────────╮
   │                                                 │
   │  Please visit: https://github.com/login/device  │
   │  Enter code: 8F43-6FCF                          │
   │                                                 │
   ├─────────────────────────────────────────────────╯
   │
   ◓  Waiting for authorization...
   ```

2. Navigate to [github.com/login/device](https://github.com/login/device) and enter the code.

3. Now run the `/models` command to select the model you want.

---

### Groq

1. Head over to the [Groq console](https://console.groq.com/), click **Create API Key**, and copy the key.

2. Run `opencode auth login` and select Groq.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● Groq
   │  ...
   └
   ```

3. Enter the API key for the provider.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  Groq
   │
   ◇  Enter your API key
   │  _
   └
   ```

4. Run the `/models` command to select the one you want.

---

### LM Studio

You can configure opencode to use local models through LM Studio.

```json title="opencode.json" "lmstudio" {5, 6, 8, 10-14}
{
  "$schema": "https://opencode.ai/config.json",
  "provider": {
    "lmstudio": {
      "npm": "@ai-sdk/openai-compatible",
      "name": "LM Studio (local)",
      "options": {
        "baseURL": "http://127.0.0.1:1234/v1"
      },
      "models": {
        "google/gemma-3n-e4b": {
          "name": "Gemma 3n-e4b (local)"
        }
      }
    }
  }
}
```

In this example:

- `lmstudio` is the custom provider ID. This can be any string you want.
- `npm` specifies the package to use for this provider. Here, `@ai-sdk/openai-compatible` is used for any OpenAI-compatible API.
- `name` is the display name for the provider in the UI.
- `options.baseURL` is the endpoint for the local server.
- `models` is a map of model IDs to their configurations. The model name will be displayed in the model selection list.

---

### Moonshot AI

To use Kimi K2 from Moonshot AI:

1. Head over to the [Moonshot AI console](https://platform.moonshot.ai/console), create an account, and click **Create API key**.

2. Run `opencode auth login` and select **Moonshot AI**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ...
   │  ● Moonshot AI
   └
   ```

3. Enter your Moonshot API key.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  Moonshot AI
   │
   ◇  Enter your API key
   │  _
   └
   ```

4. Run the `/models` command to select _Kimi K2_.

---

### Ollama

You can configure opencode to use local models through Ollama.

```json title="opencode.json" "ollama" {5, 6, 8, 10-14}
{
  "$schema": "https://opencode.ai/config.json",
  "provider": {
    "ollama": {
      "npm": "@ai-sdk/openai-compatible",
      "name": "Ollama (local)",
      "options": {
        "baseURL": "http://localhost:11434/v1"
      },
      "models": {
        "llama2": {
          "name": "Llama 2"
        }
      }
    }
  }
}
```

In this example:

- `ollama` is the custom provider ID. This can be any string you want.
- `npm` specifies the package to use for this provider. Here, `@ai-sdk/openai-compatible` is used for any OpenAI-compatible API.
- `name` is the display name for the provider in the UI.
- `options.baseURL` is the endpoint for the local server.
- `models` is a map of model IDs to their configurations. The model name will be displayed in the model selection list.

---

### OpenAI

https://platform.openai.com/api-keys

1. Head over to the [OpenAI Platform console](https://platform.openai.com/api-keys), click **Create new secret key**, and copy the key.

2. Run `opencode auth login` and select OpenAI.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● OpenAI
   │  ...
   └
   ```

3. Enter the API key for the provider.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  OpenAI
   │
   ◇  Enter your API key
   │  _
   └
   ```

4. Run the `/models` command to select the one you want.

---

### OpenRouter

1. Head over to the [OpenRouter dashboard](https://openrouter.ai/settings/keys), click **Create API Key**, and copy the key.

2. Run `opencode auth login` and select OpenRouter.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● OpenRouter
   │  ○ Anthropic
   │  ○ Google
   │  ...
   └
   ```

3. Enter the API key for the provider.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  OpenRouter
   │
   ◇  Enter your API key
   │  _
   └
   ```

4. Many OpenRouter models are preloaded by default, run the `/models` command to select the one you want.

   You can also add additional models through your opencode config.

   ```json title="opencode.json" {6}
   {
     "$schema": "https://opencode.ai/config.json",
     "provider": {
       "openrouter": {
         "models": {
           "somecoolnewmodel": {}
         }
       }
     }
   }
   ```

5. You can also customize them through your opencode config. Here's an example of specifying a provider

   ```json title="opencode.json"
   {
     "$schema": "https://opencode.ai/config.json",
     "provider": {
       "openrouter": {
         "models": {
           "moonshotai/kimi-k2": {
             "options": {
               "provider": {
                 "order": ["baseten"],
                 "allow_fallbacks": false
               }
             }
           }
         }
       }
     }
   }
   ```

---

### Together AI

1. Head over to the [Together AI console](https://api.together.ai), create an account, and click **Add Key**.

2. Run `opencode auth login` and select **Together AI**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● Together AI
   │  ...
   └
   ```

3. Enter your Together AI API key.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  Together AI
   │
   ◇  Enter your API key
   │  _
   └
   ```

4. Run the `/models` command to select a model like _Kimi K2 Instruct_.

---

### Zhipu AI

1. Head over to the [Zhipu API console](https://z.ai/manage-apikey/apikey-list), create an account, and click **Create a new API key**.

2. Run `opencode auth login` and select **Zhipu AI**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● Zhipu AI
   │  ...
   └
   ```

3. Enter your Zhipu AI API key.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  Zhipu AI
   │
   ◇  Enter your API key
   │  _
   └
   ```

4. Run the `/models` command to select a model like _GLM-4.5_.

---

## Troubleshooting

If you are having trouble with configuring a provider, check the following:

1. **Check the auth setup**: Run `opencode auth list` to see if the credentials
   for the provider are added to your config.

   This doesn't apply to providers like Amazon Bedrock, that rely on environment variables for their auth.

2. For custom providers, check the opencode config and:

   - Make sure the provider ID used in `opencode auth login` matches the ID in your opencode config.
   - The right npm package is used for the provider. For example, use `@ai-sdk/cerebras` for Cerebras. And for all other OpenAI-compatible providers, use `@ai-sdk/openai-compatible`.
   - Check correct API endpoint is used in the `options.baseURL` field.
