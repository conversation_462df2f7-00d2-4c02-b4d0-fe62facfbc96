{"name": "opencode-mobile", "main": "index.ts", "version": "0.0.1", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "db": "drizzle-kit", "update:preview": "eas update --platform ios --channel preview --environment preview -m", "update:production": "eas update --platform ios --channel production --environment production -m"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5", "@react-native-community/netinfo": "11.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@shopify/react-native-skia": "v2.0.0-next.4", "@tanstack/react-query": "^5.84.1", "axios": "^1.11.0", "color": "5.0.0", "drizzle-orm": "^0.44.4", "expo": "~53.0.20", "expo-application": "~6.1.5", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-drizzle-studio-plugin": "^0.2.0", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-keep-awake": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-network": "~7.1.5", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-sqlite": "~15.2.14", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-updates": "~0.28.17", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-edge-to-edge": "1.6.0", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-controller": "^1.18.2", "react-native-marked": "7.0.2", "react-native-nitro-modules": "^0.27.2", "react-native-pager-view": "6.7.1", "react-native-qrcode-styled": "^0.3.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-sse": "1.2.1", "react-native-svg": "15.11.2", "react-native-unistyles": "^3.0.7", "react-native-vision-camera": "^4.7.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@tanstack/eslint-plugin-query": "^5.83.1", "@types/color": "4.2.0", "@types/react": "~19.0.10", "babel-plugin-inline-import": "3.0.0", "drizzle-kit": "^0.31.4", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "5.8.2"}, "private": true}