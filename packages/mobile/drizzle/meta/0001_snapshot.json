{"version": "6", "dialect": "sqlite", "id": "e3feae20-c734-42da-b566-42b9fbbf0e82", "prevId": "69906504-785f-41f8-b137-aea78c050072", "tables": {"projects": {"name": "projects", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "server_url": {"name": "server_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "server_hostname": {"name": "server_hostname", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "server_port": {"name": "server_port", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "app_hostname": {"name": "app_hostname", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "app_git": {"name": "app_git", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "app_path_config": {"name": "app_path_config", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "app_path_data": {"name": "app_path_data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "app_path_root": {"name": "app_path_root", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "app_path_cwd": {"name": "app_path_cwd", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "app_path_state": {"name": "app_path_state", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "app_time_initialized": {"name": "app_time_initialized", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "connection_status": {"name": "connection_status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'disconnected'"}, "last_sync_timestamp": {"name": "last_sync_timestamp", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "is_favorite": {"name": "is_favorite", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_settings": {"name": "user_settings", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false, "default": 1}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'system'"}, "default_provider_id": {"name": "default_provider_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "default_model_id": {"name": "default_model_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "current_agent": {"name": "current_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'build'"}, "notifications_enabled": {"name": "notifications_enabled", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "haptics_enabled": {"name": "haptics_enabled", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "auto_sync": {"name": "auto_sync", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "cache_size_limit": {"name": "cache_size_limit", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 104857600}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "file_cache": {"name": "file_cache", "columns": {"path": {"name": "path", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_type": {"name": "file_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "git_status": {"name": "git_status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "server_modified_time": {"name": "server_modified_time", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "cached_at": {"name": "cached_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_accessed": {"name": "last_accessed", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "access_count": {"name": "access_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 1}, "is_pinned": {"name": "is_pinned", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "search_cache": {"name": "search_cache", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "query": {"name": "query", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "search_type": {"name": "search_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "results": {"name": "results", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "cached_at": {"name": "cached_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sessions": {"name": "sessions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "parent_id": {"name": "parent_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "version": {"name": "version", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "share_url": {"name": "share_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "time_created": {"name": "time_created", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "time_updated": {"name": "time_updated", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "revert_message_id": {"name": "revert_message_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "revert_part_id": {"name": "revert_part_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "revert_snapshot": {"name": "revert_snapshot", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "revert_diff": {"name": "revert_diff", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "total_cost": {"name": "total_cost", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "total_tokens_input": {"name": "total_tokens_input", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "total_tokens_output": {"name": "total_tokens_output", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "total_tokens_reasoning": {"name": "total_tokens_reasoning", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "total_tokens_cache_read": {"name": "total_tokens_cache_read", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "total_tokens_cache_write": {"name": "total_tokens_cache_write", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "message_count": {"name": "message_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "is_synced": {"name": "is_synced", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "last_sync_timestamp": {"name": "last_sync_timestamp", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_favorite": {"name": "is_favorite", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "local_notes": {"name": "local_notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "model_id": {"name": "model_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"sessions_project_id_projects_id_fk": {"name": "sessions_project_id_projects_id_fk", "tableFrom": "sessions", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "messages": {"name": "messages", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "time_created": {"name": "time_created", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('subsec') * 1000)"}, "time_completed": {"name": "time_completed", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "model_id": {"name": "model_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "mode": {"name": "mode", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "path_cwd": {"name": "path_cwd", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "path_root": {"name": "path_root", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_summary": {"name": "is_summary", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "cost": {"name": "cost", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "tokens_input": {"name": "tokens_input", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "tokens_output": {"name": "tokens_output", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "tokens_reasoning": {"name": "tokens_reasoning", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "tokens_cache_read": {"name": "tokens_cache_read", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "tokens_cache_write": {"name": "tokens_cache_write", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "error_name": {"name": "error_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "error_data": {"name": "error_data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "system_prompts": {"name": "system_prompts", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_synced": {"name": "is_synced", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "last_sync_timestamp": {"name": "last_sync_timestamp", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"messages_session_id_sessions_id_fk": {"name": "messages_session_id_sessions_id_fk", "tableFrom": "messages", "tableTo": "sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "message_parts": {"name": "message_parts", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "sequence": {"name": "sequence", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "text_content": {"name": "text_content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_synthetic": {"name": "is_synthetic", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "time_start": {"name": "time_start", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "time_end": {"name": "time_end", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_mime": {"name": "file_mime", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_filename": {"name": "file_filename", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_source_type": {"name": "file_source_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_source_path": {"name": "file_source_path", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_source_text_value": {"name": "file_source_text_value", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_source_text_start": {"name": "file_source_text_start", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_source_text_end": {"name": "file_source_text_end", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_source_name": {"name": "file_source_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_source_kind": {"name": "file_source_kind", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_source_range": {"name": "file_source_range", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_call_id": {"name": "tool_call_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_name": {"name": "tool_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_status": {"name": "tool_status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_input": {"name": "tool_input", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_output": {"name": "tool_output", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_title": {"name": "tool_title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_metadata": {"name": "tool_metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_error": {"name": "tool_error", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_time_start": {"name": "tool_time_start", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_time_end": {"name": "tool_time_end", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "step_cost": {"name": "step_cost", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "step_tokens_input": {"name": "step_tokens_input", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "step_tokens_output": {"name": "step_tokens_output", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "step_tokens_reasoning": {"name": "step_tokens_reasoning", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "step_tokens_cache_read": {"name": "step_tokens_cache_read", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "step_tokens_cache_write": {"name": "step_tokens_cache_write", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "snapshot_id": {"name": "snapshot_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "patch_hash": {"name": "patch_hash", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "patch_files": {"name": "patch_files", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_synced": {"name": "is_synced", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "last_sync_timestamp": {"name": "last_sync_timestamp", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"message_parts_session_id_sessions_id_fk": {"name": "message_parts_session_id_sessions_id_fk", "tableFrom": "message_parts", "tableTo": "sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "message_parts_message_id_messages_id_fk": {"name": "message_parts_message_id_messages_id_fk", "tableFrom": "message_parts", "tableTo": "messages", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "providers": {"name": "providers", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_available": {"name": "is_available", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "last_sync_timestamp": {"name": "last_sync_timestamp", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "event_log": {"name": "event_log", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "resource_type": {"name": "resource_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "resource_id": {"name": "resource_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sync_queue": {"name": "sync_queue", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "operation_type": {"name": "operation_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "resource_type": {"name": "resource_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "resource_id": {"name": "resource_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "payload": {"name": "payload", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "retry_count": {"name": "retry_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "max_retries": {"name": "max_retries", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 3}, "last_error": {"name": "last_error", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'pending'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "models": {"name": "models", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_name": {"name": "provider_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "context_length": {"name": "context_length", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "output_length": {"name": "output_length", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "input_price": {"name": "input_price", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "output_price": {"name": "output_price", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "cache_read_price": {"name": "cache_read_price", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "cache_write_price": {"name": "cache_write_price", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "attachment": {"name": "attachment", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "reasoning": {"name": "reasoning", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "temperature": {"name": "temperature", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "tool_call": {"name": "tool_call", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "knowledge": {"name": "knowledge", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "release_date": {"name": "release_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_updated": {"name": "last_updated", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "open_weights": {"name": "open_weights", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "is_cached": {"name": "is_cached", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "last_sync_timestamp": {"name": "last_sync_timestamp", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {"\"user_settings\".\"current_mode\"": "\"user_settings\".\"current_agent\""}}, "internal": {"indexes": {}}}