import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { eq } from "drizzle-orm"
import db from "@/db"
import { projects } from "@/db/schema"
import { queryKeys } from "../keys"
import type { Project, NewProject } from "@/db/types"

// Raw database operations (internal use)
class ProjectsRepository {
  async getAllProjects() {
    return await db.select().from(projects).orderBy(projects.createdAt)
  }

  async getActiveProject() {
    const result = await db.select().from(projects).where(eq(projects.isActive, true)).limit(1)
    return result[0] || null
  }

  async getProjectById(id: string) {
    const result = await db.select().from(projects).where(eq(projects.id, id)).limit(1)
    return result[0] || null
  }

  async createProject(projectData: Omit<NewProject, "id" | "createdAt" | "updatedAt">) {
    // Generate unique ID
    const id = `project-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`

    return await db
      .insert(projects)
      .values({
        ...projectData,
        id,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning()
  }

  async updateProject(id: string, updates: Partial<Project>) {
    return await db
      .update(projects)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(projects.id, id))
      .returning()
  }

  async deleteProject(id: string) {
    // Import schema tables for cascade delete
    const { sessions, messages, messageParts } = await import("@/db/schema")

    // Check if this is the active project
    const projectToDelete = await this.getProjectById(id)
    const isActiveProject = projectToDelete?.isActive

    // Delete all related data first (cascade delete)
    // Delete message parts for sessions belonging to this project
    const projectSessions = await db.select({ id: sessions.id }).from(sessions).where(eq(sessions.projectId, id))
    const sessionIds = projectSessions.map((s) => s.id)

    if (sessionIds.length > 0) {
      // Delete message parts for these sessions
      for (const sessionId of sessionIds) {
        await db.delete(messageParts).where(eq(messageParts.sessionId, sessionId))
      }

      // Delete messages for these sessions
      for (const sessionId of sessionIds) {
        await db.delete(messages).where(eq(messages.sessionId, sessionId))
      }
    }

    // Delete sessions belonging to this project
    await db.delete(sessions).where(eq(sessions.projectId, id))

    // Delete the project itself
    const result = await db.delete(projects).where(eq(projects.id, id))

    // Handle active project logic
    if (isActiveProject) {
      // Get remaining projects
      const remainingProjects = await this.getAllProjects()

      if (remainingProjects.length > 0) {
        // Set the first remaining project as active
        await this.setActiveProject(remainingProjects[0].id)
      } else {
        // No projects left - clear all remaining data
        await this.clearAllData()
      }
    }

    return result
  }

  private async clearAllData() {
    // Import all schema tables
    const { sessions, messages, messageParts, fileCache, providers, syncQueue } = await import("@/db/schema")

    // Clear all data when no projects remain
    await db.delete(messageParts)
    await db.delete(messages)
    await db.delete(sessions)
    await db.delete(fileCache)
    await db.delete(providers)
    await db.delete(syncQueue)
  }

  async setActiveProject(id: string) {
    // First, get the currently active project and disconnect it
    const currentActive = await this.getActiveProject()
    if (currentActive && currentActive.id !== id) {
      await this.updateConnectionStatus(currentActive.id, "disconnected")
    }

    // Deactivate all projects
    await db.update(projects).set({ isActive: false })

    // Then activate the selected project
    return await db
      .update(projects)
      .set({ isActive: true, updatedAt: new Date() })
      .where(eq(projects.id, id))
      .returning()
  }

  async updateConnectionStatus(projectId: string, status: "connected" | "disconnected" | "connecting") {
    return await db
      .update(projects)
      .set({
        connectionStatus: status,
        lastSyncTimestamp: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(projects.id, projectId))
      .returning()
  }

  async updateAppInfo(
    projectId: string,
    appInfo: {
      appHostname?: string
      appGit?: boolean
      appPathConfig?: string
      appPathData?: string
      appPathRoot?: string
      appPathCwd?: string
      appPathState?: string
      appTimeInitialized?: Date
    },
  ) {
    return await db
      .update(projects)
      .set({
        ...appInfo,
        updatedAt: new Date(),
      })
      .where(eq(projects.id, projectId))
      .returning()
  }

  async getServerUrl(projectId?: string) {
    const project = projectId ? await this.getProjectById(projectId) : await this.getActiveProject()

    if (!project) return null
    return project.serverUrl
  }
}

const projectsRepo = new ProjectsRepository()

// TanStack Query hooks for projects
export function useProjectsQuery() {
  return useQuery({
    queryKey: queryKeys.local.projects.lists(),
    queryFn: () => projectsRepo.getAllProjects(),
  })
}

export function useActiveProjectQuery() {
  return useQuery({
    queryKey: queryKeys.local.projects.active(),
    queryFn: () => projectsRepo.getActiveProject(),
  })
}

export function useProjectQuery(id: string) {
  return useQuery({
    queryKey: queryKeys.local.projects.byId(id),
    queryFn: () => projectsRepo.getProjectById(id),
    enabled: !!id,
  })
}

export function useActiveProjectServerUrlQuery() {
  return useQuery({
    queryKey: queryKeys.local.projects.activeServerUrl(),
    queryFn: () => projectsRepo.getServerUrl(),
  })
}

// Mutations for projects
export function useCreateProjectMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (projectData: Omit<NewProject, "id" | "createdAt" | "updatedAt">) =>
      projectsRepo.createProject(projectData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.all })
    },
  })
}

export function useUpdateProjectMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<Project> }) => projectsRepo.updateProject(id, updates),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.byId(id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.active() })
    },
  })
}

export function useDeleteProjectMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => projectsRepo.deleteProject(id),
    onSuccess: () => {
      // Invalidate project queries immediately
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.active() })
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.activeServerUrl() })

      // Small delay to ensure project switching completes, then invalidate session/message queries
      setTimeout(() => {
        // Invalidate session queries to refresh home screen with new active project's data
        queryClient.invalidateQueries({ queryKey: queryKeys.local.sessions.all })

        // Invalidate message queries as well since they're project-specific
        queryClient.invalidateQueries({ queryKey: queryKeys.local.messages.all })

        // Invalidate remote queries since we might have switched to a different project
        queryClient.invalidateQueries({ queryKey: queryKeys.remote.sessions.all })
        queryClient.invalidateQueries({ queryKey: queryKeys.remote.messages.all })
        queryClient.invalidateQueries({ queryKey: queryKeys.remote.config.all })
      }, 100)
    },
  })
}
export function useSetActiveProjectMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => projectsRepo.setActiveProject(id),
    onSuccess: () => {
      // Invalidate all project-related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.active() })
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.activeServerUrl() })

      // Invalidate all session queries (local and remote) since we switched projects
      queryClient.invalidateQueries({ queryKey: queryKeys.local.sessions.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.remote.sessions.all })

      // Invalidate all message queries
      queryClient.invalidateQueries({ queryKey: queryKeys.local.messages.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.remote.messages.all })

      // Invalidate remote config queries
      queryClient.invalidateQueries({ queryKey: queryKeys.remote.config.all })

      // Restart SSE service to connect to new server
      import("@/services/sse-service")
        .then(({ sseService }) => {
          sseService.disconnect()
          // Small delay to ensure clean disconnect
          setTimeout(() => {
            sseService.connect()
          }, 500)
        })
        .catch((error) => {
          console.warn("Failed to restart SSE service:", error)
        })
    },
  })
}

export function useUpdateProjectConnectionStatusMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ projectId, status }: { projectId: string; status: "connected" | "disconnected" | "connecting" }) =>
      projectsRepo.updateConnectionStatus(projectId, status),
    onSuccess: (_, { projectId }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.byId(projectId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.active() })
      // Also invalidate remote app info query to refresh connection status display
      queryClient.invalidateQueries({ queryKey: queryKeys.remote.config.app() })
    },
  })
}

export function useUpdateProjectAppInfoMutation() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({
      projectId,
      appInfo,
    }: {
      projectId: string
      appInfo: Parameters<typeof projectsRepo.updateAppInfo>[1]
    }) => projectsRepo.updateAppInfo(projectId, appInfo),
    onSuccess: (_, { projectId }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.all })
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.byId(projectId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.local.projects.active() })
    },
  })
}

// Export the repository for direct access when needed (e.g., in streaming service)
export const localProjectsService = projectsRepo
