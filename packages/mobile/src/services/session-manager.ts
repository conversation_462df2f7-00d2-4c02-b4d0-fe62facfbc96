import { router } from "expo-router"
import { useCreateRemoteSessionMutation } from "./api/remote/sessions"
import { useCreateLocalSessionMutation } from "./api/local/sessions"
import { useActiveProjectQuery } from "./api/local/projects"
import { useQueryClient } from "@tanstack/react-query"

/**
 * Session Manager - Handles session creation and navigation
 * Based on TUI's session management approach
 */
export class SessionManager {
  private createRemoteSession: ReturnType<typeof useCreateRemoteSessionMutation>["mutateAsync"]
  private createLocalSession: ReturnType<typeof useCreateLocalSessionMutation>["mutateAsync"]
  private queryClient: any
  private activeProjectId: string | null

  constructor(
    createRemoteSession: ReturnType<typeof useCreateRemoteSessionMutation>["mutateAsync"],
    createLocalSession: ReturnType<typeof useCreateLocalSessionMutation>["mutateAsync"],
    queryClient: any,
    activeProjectId: string | null,
  ) {
    this.createRemoteSession = createRemoteSession
    this.createLocalSession = createLocalSession
    this.queryClient = queryClient
    this.activeProjectId = activeProjectId
  }

  /**
   * Generates a meaningful session title with timestamp
   */
  private generateSessionTitle(customTitle?: string): string {
    if (customTitle) return customTitle

    const now = new Date()
    const timeStr = now.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    })
    const dateStr = now.toLocaleDateString([], {
      month: "short",
      day: "numeric",
    })

    return `Session ${dateStr} ${timeStr}`
  }

  /**
   * Creates a new session and navigates to it
   * Let remote server handle creation, then sync to local
   */
  async createNewSession(title?: string): Promise<string> {
    if (!this.activeProjectId) {
      throw new Error("No active project found. Please create or select a project first.")
    }

    try {
      const sessionTitle = this.generateSessionTitle(title)

      // Create session on remote server
      const remoteSession = await this.createRemoteSession({
        title: sessionTitle,
      })

      // Sync remote session to local database
      await this.createLocalSession({
        id: remoteSession.id,
        projectId: this.activeProjectId,
        parentId: remoteSession.parentId || null,
        title: remoteSession.title,
        version: remoteSession.version,
        shareUrl: remoteSession.shareUrl || null,
        timeCreated: new Date(remoteSession.time.created),
        timeUpdated: new Date(remoteSession.time.updated),
        revertMessageId: remoteSession.revertMessageId || null,
        revertPartId: remoteSession.revertPartId || null,
        revertSnapshot: remoteSession.revertSnapshot || null,
        revertDiff: remoteSession.revertDiff || null,
        totalCost: 0,
        totalTokensInput: 0,
        totalTokensOutput: 0,
        totalTokensReasoning: 0,
        totalTokensCacheRead: 0,
        totalTokensCacheWrite: 0,
        messageCount: 0,
        isSynced: true,
        lastSyncTimestamp: new Date(),
        isFavorite: false,
        localNotes: null,
      })

      // Ensure the session query is refreshed
      this.queryClient.invalidateQueries({ queryKey: ["local", "sessions", "detail", remoteSession.id] })

      return remoteSession.id
    } catch (error) {
      throw error
    }
  }
  /**
   * Navigates to a new session (creates if needed)
   */
  async navigateToNewSession(title?: string): Promise<void> {
    try {
      const sessionId = await this.createNewSession(title)
      router.push(`/chat/${sessionId}`)
    } catch (error) {
      throw error
    }
  }

  /**
   * Switches to an existing session
   */
  switchToSession(sessionId: string): void {
    router.push(`/chat/${sessionId}`)
  }

  /**
   * Clears current session and creates a new one
   * Similar to TUI's "n" key functionality
   */
  async clearAndCreateNew(): Promise<void> {
    await this.navigateToNewSession()
  }
}

/**
 * Hook to get session manager instance
 */
export function useSessionManager() {
  const createRemoteSession = useCreateRemoteSessionMutation()
  const createLocalSession = useCreateLocalSessionMutation()
  const { data: activeProject } = useActiveProjectQuery()
  const queryClient = useQueryClient()

  return new SessionManager(
    createRemoteSession.mutateAsync,
    createLocalSession.mutateAsync,
    queryClient,
    activeProject?.id || null,
  )
}
