import { memo } from "react"
import { Box, Text } from "@/components/ui/primitives"
import { <PERSON><PERSON><PERSON>nt<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>erComponent } from "./renderers"

interface FilePartRendererProps {
  part: any
  type: "file" | "snapshot" | "patch"
}

export const FilePartRenderer = memo(
  ({ part, type }: FilePartRendererProps) => {
    switch (type) {
      case "file":
        return <FileRenderer part={part} />
      case "snapshot":
        return <SnapshotRenderer part={part} />
      case "patch":
        return <PatchRenderer part={part} />
      default:
        return null
    }
  },
  (prevProps, nextProps) => {
    return (
      prevProps.type === nextProps.type &&
      prevProps.part.fileFilename === nextProps.part.fileFilename &&
      prevProps.part.fileUrl === nextProps.part.fileUrl &&
      prevProps.part.snapshotId === nextProps.part.snapshotId &&
      prevProps.part.patchHash === nextProps.part.patchHash
    )
  },
)

FilePartRenderer.displayName = "FilePartRenderer"

const FileRenderer = memo(({ part }: { part: any }) => {
  const filename = part.fileFilename
  const fileUrl = part.fileUrl
  const fileMime = part.fileMime
  const sourceType = part.fileSourceType
  const sourcePath = part.fileSourcePath
  const sourceTextStart = part.fileSourceTextStart
  const sourceTextEnd = part.fileSourceTextEnd
  const sourceName = part.fileSourceName
  const sourceRange = part.fileSourceRange
    ? typeof part.fileSourceRange === "string"
      ? JSON.parse(part.fileSourceRange)
      : part.fileSourceRange
    : null

  if (filename && fileUrl) {
    return (
      <Box>
        <FileContentRenderer filename={filename} content={fileUrl} />
        {(sourceType || sourcePath || sourceName) && (
          <Box mt="xs" p="xs" background="lighter" rounded="sm">
            <Text size="xs" mode="subtle">
              {sourceType === "symbol" && sourceName && `Symbol: ${sourceName}`}
              {sourceType === "file" && sourcePath && `Path: ${sourcePath}`}
              {sourceTextStart !== null && sourceTextEnd !== null && ` (${sourceTextStart}-${sourceTextEnd})`}
              {fileMime && ` • ${fileMime}`}
            </Text>
            {sourceRange && (
              <Text size="xs" mode="subtle" style={{ fontFamily: "monospace", marginTop: 2 }}>
                Range: {JSON.stringify(sourceRange)}
              </Text>
            )}
          </Box>
        )}
      </Box>
    )
  }

  return null
})

FileRenderer.displayName = "FileRenderer"

const SnapshotRenderer = memo(({ part }: { part: any }) => {
  const snapshotId = part.snapshotId

  if (!snapshotId) return null

  return (
    <Box background="subtle" rounded="md" p="sm">
      <Text size="xs" weight="medium" mode="subtle">
        📸 Snapshot: {snapshotId}
      </Text>
    </Box>
  )
})

SnapshotRenderer.displayName = "SnapshotRenderer"

const PatchRenderer = memo(({ part }: { part: any }) => {
  const patchHash = part.patchHash
  const patchFiles = part.patchFiles
    ? typeof part.patchFiles === "string"
      ? JSON.parse(part.patchFiles)
      : part.patchFiles
    : null

  // Add debug logging to see what data we have
  console.log("📦 FILE PATCH RENDERER DEBUG:", {
    patchHash,
    patchFiles,
    partData: part,
  })

  if (!patchHash && !patchFiles) return null

  // Use our fancy PatchRenderer component
  return <PatchRendererComponent hash={patchHash} files={patchFiles || []} patch={undefined} />
})

PatchRenderer.displayName = "PatchRenderer"
