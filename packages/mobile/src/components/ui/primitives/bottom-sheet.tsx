import { forwardRef, useImperativeHandle, useRef } from "react"
import type { ReactNode } from "react"
import BottomSheetComponent, { BottomSheetBackdrop, type BottomSheetBackdropProps } from "@gorhom/bottom-sheet"
import { useUnistyles } from "react-native-unistyles"

interface BottomSheetProps {
  children: ReactNode
  snapPoints?: string[] | number[]
  onDismiss?: () => void
  enablePanDownToClose?: boolean
  backdropOpacity?: number
  index?: number
  keyboardBehavior?: "extend" | "fillParent" | "interactive"
  keyboardBlurBehavior?: "none" | "restore"
}

export interface BottomSheetRef {
  present: () => void
  dismiss: () => void
  snapToIndex: (index: number) => void
  snapToPosition: (position: string | number) => void
}

export const BottomSheet = forwardRef<BottomSheetRef, BottomSheetProps>(
  (
    {
      children,
      snapPoints = ["60%", "95%"],
      onDismiss,
      enablePanDownToClose = true,
      backdropOpacity = 0.75,
      index = -1,
      keyboardBehavior = "interactive",
      keyboardBlurBehavior = "none",
      ...props
    },
    ref,
  ) => {
    const { theme } = useUnistyles()
    const styles = createStyles(theme)

    const bottomSheetModalRef = useRef<BottomSheetComponent>(null)

    useImperativeHandle(ref, () => ({
      present: () => bottomSheetModalRef.current?.expand(),
      dismiss: () => bottomSheetModalRef.current?.close(),
      snapToIndex: (index: number) => bottomSheetModalRef.current?.snapToIndex(index),
      snapToPosition: (position: string | number) => bottomSheetModalRef.current?.snapToPosition(position),
    }))

    const renderBackdrop = (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={backdropOpacity}
        style={[props.style, { backgroundColor: "rgba(0, 0, 0, 0.8)" }]}
      />
    )

    return (
      <BottomSheetComponent
        ref={bottomSheetModalRef}
        snapPoints={snapPoints}
        onClose={onDismiss}
        backdropComponent={renderBackdrop}
        enablePanDownToClose={enablePanDownToClose}
        index={index}
        keyboardBehavior={keyboardBehavior}
        keyboardBlurBehavior={keyboardBlurBehavior}
        style={{
          backgroundColor: theme.colors.background.base,
        }}
        containerStyle={{
          zIndex: 1000,
        }}
        backgroundStyle={{
          backgroundColor: theme.colors.background.base,
        }}
        handleIndicatorStyle={{
          backgroundColor: theme.colors.background.lightest,
        }}
        {...props}
      >
        {children}
      </BottomSheetComponent>
    )
  },
)

const createStyles = (theme: any) => ({
  background: {
    backgroundColor: theme.colors.background.base,
    borderTopLeftRadius: theme.radius.lg,
    borderTopRightRadius: theme.radius.lg,
    borderWidth: 1,
    borderBottomWidth: 0,
    borderColor: theme.colors.border.subtle,
  },
  handleIndicator: {
    backgroundColor: theme.colors.border.default,
    width: 40,
    height: 4,
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.base,
  },
})

BottomSheet.displayName = "BottomSheet"
